import React, { useState, useRef, useEffect } from 'react';
import './PWAMobileCard.css';

/**
 * Modern 2025 PWA Mobile Card Component
 * Implements swipe gestures, haptic feedback, and modern mobile interactions
 */
const PWAMobileCard = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onTap,
  className = '',
  swipeEnabled = true,
  hapticEnabled = true,
  variant = 'default', // 'default', 'story', 'discussion', 'support'
  priority = 'normal', // 'high', 'normal', 'low'
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [swipeDirection, setSwipeDirection] = useState(null);
  const cardRef = useRef(null);
  const touchStartRef = useRef({ x: 0, y: 0, time: 0 });
  const touchMoveRef = useRef({ x: 0, y: 0 });

  const triggerHaptic = (intensity = 'light') => {
    if (!hapticEnabled || !('vibrate' in navigator)) return;
    
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [50]
    };
    
    navigator.vibrate(patterns[intensity] || patterns.light);
  };

  const handleTouchStart = (e) => {
    if (!swipeEnabled) return;
    
    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
    
    setIsPressed(true);
    triggerHaptic('light');
  };

  const handleTouchMove = (e) => {
    if (!swipeEnabled || !isPressed) return;
    
    const touch = e.touches[0];
    touchMoveRef.current = {
      x: touch.clientX,
      y: touch.clientY
    };
    
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    
    // Only handle horizontal swipes
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 20) {
      e.preventDefault();
      
      // Visual feedback during swipe
      if (cardRef.current) {
        const progress = Math.min(Math.abs(deltaX) / 100, 1);
        const direction = deltaX > 0 ? 'right' : 'left';
        
        cardRef.current.style.transform = `translateX(${deltaX * 0.3}px)`;
        cardRef.current.style.opacity = 1 - (progress * 0.3);
        
        // Update swipe direction for visual feedback
        setSwipeDirection(direction);
      }
    }
  };

  const handleTouchEnd = (e) => {
    if (!swipeEnabled) return;
    
    setIsPressed(false);
    setSwipeDirection(null);
    
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;
    
    // Reset card position
    if (cardRef.current) {
      cardRef.current.style.transform = '';
      cardRef.current.style.opacity = '';
    }
    
    // Determine if it's a swipe or tap
    const isSwipe = Math.abs(deltaX) > 50 && Math.abs(deltaX) > Math.abs(deltaY);
    const isTap = Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300;
    
    if (isSwipe) {
      triggerHaptic('medium');
      
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    } else if (isTap && onTap) {
      triggerHaptic('light');
      onTap();
    }
  };

  const cardClasses = [
    'pwa-mobile-card',
    `pwa-mobile-card--${variant}`,
    `pwa-mobile-card--${priority}`,
    isPressed ? 'pwa-mobile-card--pressed' : '',
    swipeDirection ? `pwa-mobile-card--swipe-${swipeDirection}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={cardRef}
      className={cardClasses}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      role="article"
      tabIndex={0}
      {...props}
    >
      {/* Swipe indicators */}
      {swipeEnabled && (
        <>
          <div className="pwa-mobile-card__swipe-indicator pwa-mobile-card__swipe-indicator--left">
            <span>👈</span>
          </div>
          <div className="pwa-mobile-card__swipe-indicator pwa-mobile-card__swipe-indicator--right">
            <span>👉</span>
          </div>
        </>
      )}
      
      {/* Priority indicator */}
      {priority === 'high' && (
        <div className="pwa-mobile-card__priority-indicator">
          <span>⚡</span>
        </div>
      )}
      
      {/* Card content */}
      <div className="pwa-mobile-card__content">
        {children}
      </div>
      
      {/* Interaction hints */}
      {swipeEnabled && (
        <div className="pwa-mobile-card__hints">
          <div className="pwa-mobile-card__hint pwa-mobile-card__hint--left">
            Swipe left
          </div>
          <div className="pwa-mobile-card__hint pwa-mobile-card__hint--right">
            Swipe right
          </div>
        </div>
      )}
    </div>
  );
};

// Story Card Variant
export const PWAStoryCard = ({ story, onLike, onShare, onComment, ...props }) => (
  <PWAMobileCard
    variant="story"
    onSwipeRight={onLike}
    onSwipeLeft={onShare}
    onTap={onComment}
    {...props}
  >
    <div className="pwa-story-card">
      <div className="pwa-story-card__header">
        <div className="pwa-story-card__avatar">
          {story.author?.name?.charAt(0) || '👤'}
        </div>
        <div className="pwa-story-card__meta">
          <h3 className="pwa-story-card__author">{story.author?.name || 'Anonymous'}</h3>
          <p className="pwa-story-card__date">{story.createdAt}</p>
        </div>
      </div>
      
      <div className="pwa-story-card__content">
        <h4 className="pwa-story-card__title">{story.title}</h4>
        <p className="pwa-story-card__excerpt">{story.excerpt}</p>
      </div>
      
      <div className="pwa-story-card__actions">
        <button className="pwa-story-card__action">
          <span>❤️</span>
          <span>{story.likes || 0}</span>
        </button>
        <button className="pwa-story-card__action">
          <span>💬</span>
          <span>{story.comments || 0}</span>
        </button>
        <button className="pwa-story-card__action">
          <span>📤</span>
          <span>Share</span>
        </button>
      </div>
    </div>
  </PWAMobileCard>
);

// Discussion Card Variant
export const PWADiscussionCard = ({ discussion, onJoin, onBookmark, ...props }) => (
  <PWAMobileCard
    variant="discussion"
    onSwipeRight={onJoin}
    onSwipeLeft={onBookmark}
    {...props}
  >
    <div className="pwa-discussion-card">
      <div className="pwa-discussion-card__header">
        <span className="pwa-discussion-card__category">{discussion.category}</span>
        <span className="pwa-discussion-card__participants">
          {discussion.participants} participants
        </span>
      </div>
      
      <h4 className="pwa-discussion-card__title">{discussion.title}</h4>
      <p className="pwa-discussion-card__preview">{discussion.preview}</p>
      
      <div className="pwa-discussion-card__footer">
        <span className="pwa-discussion-card__time">{discussion.lastActivity}</span>
        <span className="pwa-discussion-card__status">
          {discussion.isActive ? '🟢 Active' : '⚪ Quiet'}
        </span>
      </div>
    </div>
  </PWAMobileCard>
);

export default PWAMobileCard;
