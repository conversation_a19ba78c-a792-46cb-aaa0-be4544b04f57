import React, { useState, useEffect } from 'react';
import PWABottomNavigation from './PWABottomNavigation';
import PWAMobileCard, { PWAStoryCard, PWADiscussionCard } from './PWAMobileCard';
import './PWAMobileLayout.css';

/**
 * PWA Mobile Layout Component
 * Wraps existing NAROOP content with modern 2025 PWA mobile patterns
 * Only applies on mobile devices - desktop remains unchanged
 */
const PWAMobileLayout = ({
  children,
  currentSection = 'home',
  onNavigate,
  userRole,
  ADMIN_ROLES,
  isAuthenticated = false,
  stories = [],
  discussions = [],
  onRefresh,
  className = ''
}) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle PWA refresh event
  useEffect(() => {
    const handlePWARefresh = async (event) => {
      setIsRefreshing(true);
      
      try {
        if (onRefresh) {
          await onRefresh();
        }
        
        // Simulate refresh delay for better UX
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      } catch (error) {
        console.error('Refresh failed:', error);
        setIsRefreshing(false);
      }
    };

    document.addEventListener('pwa-refresh', handlePWARefresh);
    return () => document.removeEventListener('pwa-refresh', handlePWARefresh);
  }, [onRefresh]);

  // Handle swipe actions on cards
  const handleStorySwipe = (story, direction) => {
    if (direction === 'right') {
      // Like story
      console.log('Liked story:', story.id);
    } else if (direction === 'left') {
      // Share story
      console.log('Shared story:', story.id);
    }
  };

  const handleDiscussionSwipe = (discussion, direction) => {
    if (direction === 'right') {
      // Join discussion
      console.log('Joined discussion:', discussion.id);
    } else if (direction === 'left') {
      // Bookmark discussion
      console.log('Bookmarked discussion:', discussion.id);
    }
  };

  // If not mobile, render children without PWA wrapper
  if (!isMobile) {
    return <>{children}</>;
  }

  return (
    <div className={`pwa-mobile-layout ${className}`}>
      {/* PWA App Shell */}
      <div className="pwa-app-shell">
        
        {/* Main Content Area */}
        <main className="pwa-main-content">
          
          {/* Refresh Indicator */}
          {isRefreshing && (
            <div className="pwa-refresh-indicator">
              <div className="pwa-refresh-spinner">⟳</div>
              <span>Refreshing...</span>
            </div>
          )}
          
          {/* Content Cards Stack */}
          <div className="pwa-card-stack">
            
            {/* Render Stories as PWA Cards */}
            {stories.map((story) => (
              <PWAStoryCard
                key={story.id}
                story={story}
                onLike={() => handleStorySwipe(story, 'right')}
                onShare={() => handleStorySwipe(story, 'left')}
                onComment={() => console.log('Comment on story:', story.id)}
              />
            ))}
            
            {/* Render Discussions as PWA Cards */}
            {discussions.map((discussion) => (
              <PWADiscussionCard
                key={discussion.id}
                discussion={discussion}
                onJoin={() => handleDiscussionSwipe(discussion, 'right')}
                onBookmark={() => handleDiscussionSwipe(discussion, 'left')}
              />
            ))}
            
            {/* Render other content as PWA Cards */}
            {React.Children.map(children, (child, index) => {
              if (React.isValidElement(child)) {
                return (
                  <PWAMobileCard
                    key={index}
                    onTap={() => console.log('Tapped card:', index)}
                    swipeEnabled={false}
                  >
                    {child}
                  </PWAMobileCard>
                );
              }
              return child;
            })}
            
            {/* Loading placeholder for infinite scroll */}
            <div className="pwa-infinite-scroll-sentinel" />
            
          </div>
        </main>
        
        {/* PWA Bottom Navigation */}
        <PWABottomNavigation
          currentSection={currentSection}
          onNavigate={onNavigate}
          userRole={userRole}
          ADMIN_ROLES={ADMIN_ROLES}
          isAuthenticated={isAuthenticated}
        />
        
        {/* Floating Action Button for Quick Story Share */}
        {isAuthenticated && (
          <button
            className="pwa-fab"
            onClick={() => setShowQuickActions(true)}
            aria-label="Quick actions"
            title="Quick actions"
          >
            <span className="pwa-fab__icon">✨</span>
          </button>
        )}
        
        {/* Quick Actions Bottom Sheet */}
        <div 
          id="pwa-quick-actions" 
          className={`pwa-bottom-sheet ${showQuickActions ? 'pwa-bottom-sheet--open' : ''}`}
        >
          <div 
            className="pwa-bottom-sheet__handle"
            onClick={() => setShowQuickActions(false)}
          />
          <div className="pwa-bottom-sheet__content">
            <h3 className="pwa-bottom-sheet__title">Quick Actions</h3>
            
            <div className="pwa-quick-actions">
              <button 
                className="pwa-button pwa-button--primary"
                onClick={() => {
                  setShowQuickActions(false);
                  onNavigate && onNavigate('share-story');
                }}
              >
                <span>📝</span>
                Share Your Story
              </button>
              
              <button 
                className="pwa-button pwa-button--secondary"
                onClick={() => {
                  setShowQuickActions(false);
                  onNavigate && onNavigate('discussions');
                }}
              >
                <span>💬</span>
                Start Discussion
              </button>
              
              <button 
                className="pwa-button pwa-button--secondary"
                onClick={() => {
                  setShowQuickActions(false);
                  onNavigate && onNavigate('support');
                }}
              >
                <span>🤝</span>
                Request Support
              </button>
              
              <button 
                className="pwa-button pwa-button--secondary"
                onClick={() => {
                  setShowQuickActions(false);
                  onNavigate && onNavigate('business');
                }}
              >
                <span>💼</span>
                Business Directory
              </button>
            </div>
          </div>
        </div>
        
        {/* Bottom Sheet Overlay */}
        {showQuickActions && (
          <div 
            className="pwa-bottom-sheet-overlay"
            onClick={() => setShowQuickActions(false)}
          />
        )}
        
      </div>
    </div>
  );
};

// Higher-order component to conditionally wrap with PWA layout
export const withPWAMobile = (WrappedComponent) => {
  return (props) => {
    const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

    useEffect(() => {
      const handleResize = () => {
        setIsMobile(window.innerWidth <= 768);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    if (isMobile) {
      return (
        <PWAMobileLayout {...props}>
          <WrappedComponent {...props} />
        </PWAMobileLayout>
      );
    }

    return <WrappedComponent {...props} />;
  };
};

export default PWAMobileLayout;
