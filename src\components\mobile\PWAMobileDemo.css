/* NAROOP PWA Mobile Demo Styles */
/* Preserving NAROOP color palette: #FDFBF5, #591C28, #6E8C65, #F7D046 */

/* ===== DEMO CONTAINER ===== */
.pwa-mobile-demo {
  width: 100%;
  min-height: 100vh;
}

/* ===== DEMO CONTENT ===== */
.pwa-demo-content {
  width: 100%;
  max-width: 100%;
}

.pwa-demo-section {
  margin-bottom: 24px;
  padding: 0;
}

.pwa-demo-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin: 0 0 12px;
  text-align: center;
  background: linear-gradient(135deg, var(--color-heritage-gold), #F7D046);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pwa-demo-subtitle {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0 0 16px;
  text-align: left;
}

.pwa-demo-description {
  font-size: 14px;
  color: var(--color-heritage-forest);
  line-height: 1.6;
  margin: 0;
  text-align: center;
  padding: 0 8px;
}

/* ===== FEATURE HIGHLIGHTS ===== */
.pwa-demo-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.pwa-demo-feature {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(89, 28, 40, 0.08);
  transition: all 200ms ease;
}

.pwa-demo-feature:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.08);
}

.pwa-demo-feature-icon {
  font-size: 24px;
  line-height: 1;
  flex-shrink: 0;
}

.pwa-demo-feature strong {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin-bottom: 4px;
}

.pwa-demo-feature p {
  font-size: 12px;
  color: var(--color-heritage-forest);
  line-height: 1.4;
  margin: 0;
}

/* ===== INSTRUCTIONS ===== */
.pwa-demo-instructions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.pwa-demo-instruction {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: rgba(247, 208, 70, 0.1);
  border-radius: 8px;
  border-left: 3px solid var(--color-heritage-gold);
}

.pwa-demo-instruction span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.pwa-demo-instruction p {
  font-size: 13px;
  color: var(--color-heritage-black);
  line-height: 1.4;
  margin: 0;
  flex: 1;
}

/* ===== COMMUNITY STATS ===== */
.pwa-demo-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.pwa-demo-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  background: rgba(110, 140, 101, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(110, 140, 101, 0.2);
  text-align: center;
}

.pwa-demo-stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-heritage-forest);
  line-height: 1;
  margin-bottom: 4px;
}

.pwa-demo-stat-label {
  font-size: 10px;
  font-weight: 600;
  color: var(--color-heritage-forest);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 320px) {
  .pwa-demo-title {
    font-size: 20px;
  }
  
  .pwa-demo-subtitle {
    font-size: 16px;
  }
  
  .pwa-demo-features {
    gap: 12px;
  }
  
  .pwa-demo-feature {
    padding: 12px;
  }
  
  .pwa-demo-feature-icon {
    font-size: 20px;
  }
  
  .pwa-demo-stats {
    gap: 8px;
  }
  
  .pwa-demo-stat {
    padding: 12px 6px;
  }
  
  .pwa-demo-stat-number {
    font-size: 16px;
  }
  
  .pwa-demo-stat-label {
    font-size: 9px;
  }
}

@media (min-width: 375px) and (max-width: 768px) {
  .pwa-demo-features {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pwa-demo-feature {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .pwa-demo-feature-icon {
    align-self: center;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (max-height: 500px) and (orientation: landscape) {
  .pwa-demo-section {
    margin-bottom: 16px;
  }
  
  .pwa-demo-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .pwa-demo-feature {
    padding: 12px;
  }
  
  .pwa-demo-instructions {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pwa-demo-instruction {
    padding: 8px;
  }
}

/* ===== ACCESSIBILITY ===== */
.pwa-demo-feature:focus,
.pwa-demo-instruction:focus {
  outline: 2px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .pwa-demo-feature {
    transition: none;
  }
  
  .pwa-demo-feature:hover {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .pwa-demo-feature {
    border-width: 2px;
    background: rgba(255, 255, 255, 0.9);
  }
  
  .pwa-demo-instruction {
    border-left-width: 4px;
    background: rgba(247, 208, 70, 0.2);
  }
  
  .pwa-demo-stat {
    border-width: 2px;
    background: rgba(110, 140, 101, 0.2);
  }
}

/* ===== DESKTOP OVERRIDE ===== */
@media (min-width: 769px) {
  .pwa-mobile-demo {
    /* On desktop, show a message that this is mobile-only */
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    padding: 40px;
  }
  
  .pwa-demo-content {
    max-width: 600px;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(89, 28, 40, 0.12);
  }
  
  .pwa-demo-content::before {
    content: "📱 PWA Mobile Experience";
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--color-heritage-gold);
    margin-bottom: 16px;
  }
  
  .pwa-demo-content::after {
    content: "Please view on a mobile device or resize your browser window to less than 768px to experience the PWA mobile patterns.";
    display: block;
    font-size: 16px;
    color: var(--color-heritage-forest);
    margin-top: 16px;
    line-height: 1.5;
  }
  
  .pwa-demo-section {
    display: none;
  }
}
