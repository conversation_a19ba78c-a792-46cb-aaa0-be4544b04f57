/**
 * PWA Mobile 2025 Implementation Tests
 * Verifies mobile PWA patterns work correctly while preserving desktop experience
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PWAMobileLayout from '../components/mobile/PWAMobileLayout';
import PWABottomNavigation from '../components/mobile/PWABottomNavigation';
import PWAMobileCard, { PWAStoryCard, PWADiscussionCard } from '../components/mobile/PWAMobileCard';
import PWAMobileDemo from '../components/mobile/PWAMobileDemo';

// Mock window.innerWidth for responsive testing
const mockWindowWidth = (width) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  window.dispatchEvent(new Event('resize'));
};

// Mock navigator.vibrate for haptic feedback testing
const mockVibrate = jest.fn();
Object.defineProperty(navigator, 'vibrate', {
  writable: true,
  value: mockVibrate,
});

describe('PWA Mobile 2025 Implementation', () => {
  
  describe('Responsive Behavior', () => {
    test('PWA patterns only apply on mobile devices', () => {
      // Test desktop (should not show PWA patterns)
      mockWindowWidth(1024);
      const { container } = render(
        <PWAMobileLayout>
          <div>Desktop Content</div>
        </PWAMobileLayout>
      );
      
      expect(container.querySelector('.pwa-bottom-nav')).not.toBeInTheDocument();
      expect(container.querySelector('.pwa-fab')).not.toBeInTheDocument();
      
      // Test mobile (should show PWA patterns)
      mockWindowWidth(375);
      const { container: mobileContainer } = render(
        <PWAMobileLayout isAuthenticated={true}>
          <div>Mobile Content</div>
        </PWAMobileLayout>
      );
      
      expect(mobileContainer.querySelector('.pwa-app-shell')).toBeInTheDocument();
    });

    test('desktop layout remains completely unchanged', () => {
      mockWindowWidth(1200);
      const { container } = render(
        <PWAMobileLayout>
          <div className="desktop-content">Original Content</div>
        </PWAMobileLayout>
      );
      
      // Should render children directly without PWA wrapper
      expect(container.querySelector('.desktop-content')).toBeInTheDocument();
      expect(container.querySelector('.pwa-app-shell')).not.toBeInTheDocument();
    });
  });

  describe('Bottom Navigation', () => {
    beforeEach(() => {
      mockWindowWidth(375); // Mobile width
    });

    test('renders navigation items correctly', () => {
      const mockNavigate = jest.fn();
      render(
        <PWABottomNavigation
          currentSection="home"
          onNavigate={mockNavigate}
          isAuthenticated={true}
        />
      );

      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('Stories')).toBeInTheDocument();
      expect(screen.getByText('Connect')).toBeInTheDocument();
      expect(screen.getByText('Kids')).toBeInTheDocument(); // Shows when authenticated
      expect(screen.getByText('Profile')).toBeInTheDocument();
    });

    test('handles navigation correctly', () => {
      const mockNavigate = jest.fn();
      render(
        <PWABottomNavigation
          currentSection="home"
          onNavigate={mockNavigate}
          isAuthenticated={true}
        />
      );

      fireEvent.click(screen.getByText('Stories'));
      expect(mockNavigate).toHaveBeenCalledWith('stories');
    });

    test('triggers haptic feedback on navigation', () => {
      const mockNavigate = jest.fn();
      render(
        <PWABottomNavigation
          currentSection="home"
          onNavigate={mockNavigate}
          isAuthenticated={true}
        />
      );

      fireEvent.click(screen.getByText('Stories'));
      expect(mockVibrate).toHaveBeenCalledWith([10]);
    });
  });

  describe('PWA Mobile Cards', () => {
    beforeEach(() => {
      mockWindowWidth(375);
      mockVibrate.mockClear();
    });

    test('renders story card with correct content', () => {
      const story = {
        id: 1,
        title: 'Test Story',
        excerpt: 'Test excerpt',
        author: { name: 'Test Author' },
        createdAt: '1 hour ago',
        likes: 5,
        comments: 2
      };

      render(
        <PWAStoryCard
          story={story}
          onLike={jest.fn()}
          onShare={jest.fn()}
          onComment={jest.fn()}
        />
      );

      expect(screen.getByText('Test Story')).toBeInTheDocument();
      expect(screen.getByText('Test excerpt')).toBeInTheDocument();
      expect(screen.getByText('Test Author')).toBeInTheDocument();
    });

    test('handles swipe gestures correctly', async () => {
      const mockSwipeRight = jest.fn();
      const mockSwipeLeft = jest.fn();
      
      render(
        <PWAMobileCard
          onSwipeRight={mockSwipeRight}
          onSwipeLeft={mockSwipeLeft}
        >
          <div>Card Content</div>
        </PWAMobileCard>
      );

      const card = screen.getByRole('article');
      
      // Simulate right swipe
      fireEvent.touchStart(card, {
        touches: [{ clientX: 100, clientY: 100 }]
      });
      fireEvent.touchEnd(card, {
        changedTouches: [{ clientX: 200, clientY: 100 }]
      });

      await waitFor(() => {
        expect(mockSwipeRight).toHaveBeenCalled();
        expect(mockVibrate).toHaveBeenCalledWith([20]); // Medium haptic
      });
    });

    test('handles tap gestures correctly', async () => {
      const mockTap = jest.fn();
      
      render(
        <PWAMobileCard onTap={mockTap}>
          <div>Card Content</div>
        </PWAMobileCard>
      );

      const card = screen.getByRole('article');
      
      // Simulate tap (short touch with minimal movement)
      fireEvent.touchStart(card, {
        touches: [{ clientX: 100, clientY: 100 }]
      });
      fireEvent.touchEnd(card, {
        changedTouches: [{ clientX: 105, clientY: 105 }]
      });

      await waitFor(() => {
        expect(mockTap).toHaveBeenCalled();
        expect(mockVibrate).toHaveBeenCalledWith([10]); // Light haptic
      });
    });
  });

  describe('Pull to Refresh', () => {
    beforeEach(() => {
      mockWindowWidth(375);
    });

    test('triggers refresh on pull gesture', async () => {
      const mockRefresh = jest.fn();
      render(
        <PWAMobileLayout onRefresh={mockRefresh}>
          <div>Content</div>
        </PWAMobileLayout>
      );

      // Simulate pull to refresh
      const customEvent = new CustomEvent('pwa-refresh', {
        detail: { timestamp: Date.now() }
      });
      
      document.dispatchEvent(customEvent);

      await waitFor(() => {
        expect(mockRefresh).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      mockWindowWidth(375);
    });

    test('maintains proper ARIA labels', () => {
      render(
        <PWABottomNavigation
          currentSection="home"
          onNavigate={jest.fn()}
          isAuthenticated={true}
        />
      );

      const homeButton = screen.getByLabelText(/Navigate to Home/);
      expect(homeButton).toHaveAttribute('aria-current', 'page');
    });

    test('meets minimum touch target sizes', () => {
      render(
        <PWABottomNavigation
          currentSection="home"
          onNavigate={jest.fn()}
          isAuthenticated={true}
        />
      );

      const navItems = document.querySelectorAll('.pwa-bottom-nav__item');
      navItems.forEach(item => {
        const styles = window.getComputedStyle(item);
        const minWidth = parseInt(styles.minWidth);
        const minHeight = parseInt(styles.minHeight);
        
        expect(minWidth).toBeGreaterThanOrEqual(44); // 44px minimum
        expect(minHeight).toBeGreaterThanOrEqual(44);
      });
    });

    test('supports keyboard navigation', () => {
      render(
        <PWAMobileCard onTap={jest.fn()}>
          <div>Card Content</div>
        </PWAMobileCard>
      );

      const card = screen.getByRole('article');
      expect(card).toHaveAttribute('tabIndex', '0');
    });
  });

  describe('Performance', () => {
    test('uses hardware acceleration classes', () => {
      mockWindowWidth(375);
      const { container } = render(
        <PWAMobileLayout>
          <div>Content</div>
        </PWAMobileLayout>
      );

      const appShell = container.querySelector('.pwa-app-shell');
      expect(appShell).toBeInTheDocument();
      
      // Check for performance optimization classes
      const cards = container.querySelectorAll('.pwa-mobile-card');
      cards.forEach(card => {
        const styles = window.getComputedStyle(card);
        expect(styles.contain).toContain('layout');
      });
    });
  });

  describe('NAROOP Design Preservation', () => {
    test('uses correct NAROOP color palette', () => {
      mockWindowWidth(375);
      render(
        <PWABottomNavigation
          currentSection="home"
          onNavigate={jest.fn()}
          isAuthenticated={true}
        />
      );

      const nav = document.querySelector('.pwa-bottom-nav');
      const styles = window.getComputedStyle(nav);
      
      // Should use NAROOP cream background
      expect(styles.background).toContain('253, 251, 245'); // #FDFBF5
    });

    test('maintains cultural authenticity in content', () => {
      const story = {
        id: 1,
        title: 'Community Story',
        excerpt: 'A story about our community',
        author: { name: 'Community Member' },
        createdAt: '1 hour ago',
        likes: 5,
        comments: 2
      };

      render(
        <PWAStoryCard
          story={story}
          onLike={jest.fn()}
          onShare={jest.fn()}
          onComment={jest.fn()}
        />
      );

      // Content should be preserved exactly as provided
      expect(screen.getByText('Community Story')).toBeInTheDocument();
      expect(screen.getByText('A story about our community')).toBeInTheDocument();
    });
  });

  describe('Demo Component', () => {
    test('renders demo content correctly', () => {
      render(<PWAMobileDemo />);
      
      expect(screen.getByText(/Welcome to NAROOP PWA/)).toBeInTheDocument();
      expect(screen.getByText(/PWA Features/)).toBeInTheDocument();
    });

    test('shows desktop message on large screens', () => {
      mockWindowWidth(1024);
      const { container } = render(<PWAMobileDemo />);
      
      // Should show desktop-specific styling
      expect(container.querySelector('.pwa-mobile-demo')).toBeInTheDocument();
    });
  });
});

// Integration test for complete PWA experience
describe('PWA Integration', () => {
  test('complete mobile experience works together', async () => {
    mockWindowWidth(375);
    
    const mockNavigate = jest.fn();
    const mockRefresh = jest.fn();
    
    const stories = [{
      id: 1,
      title: 'Test Story',
      excerpt: 'Test content',
      author: { name: 'Author' },
      createdAt: '1 hour ago',
      likes: 5,
      comments: 2
    }];

    render(
      <PWAMobileLayout
        currentSection="home"
        onNavigate={mockNavigate}
        onRefresh={mockRefresh}
        isAuthenticated={true}
        stories={stories}
      >
        <div>Additional Content</div>
      </PWAMobileLayout>
    );

    // Verify all PWA components are present
    expect(document.querySelector('.pwa-app-shell')).toBeInTheDocument();
    expect(document.querySelector('.pwa-bottom-nav')).toBeInTheDocument();
    expect(document.querySelector('.pwa-fab')).toBeInTheDocument();
    expect(screen.getByText('Test Story')).toBeInTheDocument();

    // Test navigation
    fireEvent.click(screen.getByText('Stories'));
    expect(mockNavigate).toHaveBeenCalledWith('stories');

    // Test refresh
    const refreshEvent = new CustomEvent('pwa-refresh');
    document.dispatchEvent(refreshEvent);
    
    await waitFor(() => {
      expect(mockRefresh).toHaveBeenCalled();
    });
  });
});
