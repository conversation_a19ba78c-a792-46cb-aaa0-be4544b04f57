/* NAROOP 2025 PWA Mobile Design Patterns */
/* Preserving NAROOP color palette: #FDFBF5, #591C28, #6E8C65, #F7D046 */

/* ===== PWA DESIGN TOKENS ===== */
:root {
  /* 2025 PWA Spacing Scale */
  --pwa-space-micro: 2px;
  --pwa-space-xs: 4px;
  --pwa-space-sm: 8px;
  --pwa-space-md: 16px;
  --pwa-space-lg: 24px;
  --pwa-space-xl: 32px;
  --pwa-space-2xl: 48px;
  
  /* Modern Touch Targets (2025 Standards) */
  --pwa-touch-min: 44px;
  --pwa-touch-comfortable: 48px;
  --pwa-touch-large: 56px;
  --pwa-touch-xl: 64px;
  
  /* PWA Animation Timing */
  --pwa-transition-fast: 150ms;
  --pwa-transition-normal: 250ms;
  --pwa-transition-slow: 350ms;
  --pwa-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
  --pwa-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Modern Border Radius Scale */
  --pwa-radius-sm: 8px;
  --pwa-radius-md: 12px;
  --pwa-radius-lg: 16px;
  --pwa-radius-xl: 24px;
  --pwa-radius-pill: 50px;
  
  /* PWA Shadows (2025 Depth System) */
  --pwa-shadow-sm: 0 1px 3px rgba(89, 28, 40, 0.08);
  --pwa-shadow-md: 0 4px 12px rgba(89, 28, 40, 0.12);
  --pwa-shadow-lg: 0 8px 24px rgba(89, 28, 40, 0.16);
  --pwa-shadow-xl: 0 16px 48px rgba(89, 28, 40, 0.20);
  
  /* PWA Z-Index Scale */
  --pwa-z-base: 1;
  --pwa-z-elevated: 10;
  --pwa-z-sticky: 100;
  --pwa-z-overlay: 1000;
  --pwa-z-modal: 2000;
  --pwa-z-toast: 3000;
}

/* ===== MOBILE-ONLY PWA PATTERNS ===== */
/* Only apply these styles on mobile devices */
@media (max-width: 768px) {
  
  /* PWA App Shell */
  .pwa-app-shell {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: var(--color-heritage-cream);
    position: relative;
    overflow-x: hidden;
  }
  
  /* Modern Mobile Header */
  .pwa-header {
    position: sticky;
    top: 0;
    z-index: var(--pwa-z-sticky);
    background: rgba(253, 251, 245, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(89, 28, 40, 0.08);
    padding: var(--pwa-space-sm) var(--pwa-space-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--pwa-touch-large);
  }
  
  /* PWA Bottom Navigation (2025 Pattern) */
  .pwa-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--pwa-z-sticky);
    background: rgba(253, 251, 245, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(89, 28, 40, 0.08);
    padding: var(--pwa-space-sm) var(--pwa-space-md);
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: var(--pwa-shadow-lg);
  }
  
  .pwa-bottom-nav__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--pwa-space-xs);
    padding: var(--pwa-space-xs);
    min-width: var(--pwa-touch-comfortable);
    min-height: var(--pwa-touch-comfortable);
    border-radius: var(--pwa-radius-md);
    transition: all var(--pwa-transition-normal) var(--pwa-ease-out);
    color: var(--color-heritage-forest);
    text-decoration: none;
    position: relative;
  }
  
  .pwa-bottom-nav__item--active {
    background: rgba(247, 208, 70, 0.15);
    color: var(--color-heritage-black);
    transform: translateY(-2px);
  }
  
  .pwa-bottom-nav__item--active::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: var(--color-heritage-gold);
    border-radius: var(--pwa-radius-pill);
  }
  
  .pwa-bottom-nav__icon {
    width: 24px;
    height: 24px;
    transition: transform var(--pwa-transition-fast) var(--pwa-spring);
  }
  
  .pwa-bottom-nav__item--active .pwa-bottom-nav__icon {
    transform: scale(1.1);
  }
  
  .pwa-bottom-nav__label {
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    line-height: 1;
  }
  
  /* Floating Action Button (2025 Pattern) */
  .pwa-fab {
    position: fixed;
    bottom: calc(var(--pwa-touch-xl) + var(--pwa-space-lg));
    right: var(--pwa-space-md);
    z-index: var(--pwa-z-elevated);
    width: var(--pwa-touch-large);
    height: var(--pwa-touch-large);
    border-radius: var(--pwa-radius-pill);
    background: linear-gradient(135deg, var(--color-heritage-gold), #F7D046);
    border: none;
    box-shadow: var(--pwa-shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-heritage-black);
    font-size: 24px;
    cursor: pointer;
    transition: all var(--pwa-transition-normal) var(--pwa-spring);
  }
  
  .pwa-fab:hover,
  .pwa-fab:focus {
    transform: scale(1.05) translateY(-2px);
    box-shadow: var(--pwa-shadow-xl);
  }
  
  .pwa-fab:active {
    transform: scale(0.95);
  }
  
  /* Modern Card Stack Pattern */
  .pwa-card-stack {
    display: flex;
    flex-direction: column;
    gap: var(--pwa-space-md);
    padding: var(--pwa-space-md);
    padding-bottom: calc(var(--pwa-touch-xl) + var(--pwa-space-2xl));
  }
  
  .pwa-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--pwa-radius-lg);
    padding: var(--pwa-space-lg);
    box-shadow: var(--pwa-shadow-sm);
    border: 1px solid rgba(89, 28, 40, 0.06);
    transition: all var(--pwa-transition-normal) var(--pwa-ease-out);
    position: relative;
    overflow: hidden;
  }
  
  .pwa-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--pwa-shadow-md);
  }
  
  .pwa-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-heritage-gold), var(--color-heritage-forest));
    opacity: 0;
    transition: opacity var(--pwa-transition-normal);
  }
  
  .pwa-card:hover::before {
    opacity: 1;
  }
  
  /* Pull-to-Refresh Indicator */
  .pwa-pull-refresh {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border-radius: var(--pwa-radius-pill);
    background: rgba(247, 208, 70, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-heritage-black);
    font-size: 18px;
    transition: all var(--pwa-transition-normal) var(--pwa-spring);
    opacity: 0;
    pointer-events: none;
  }
  
  .pwa-pull-refresh--active {
    opacity: 1;
    transform: translateX(-50%) translateY(20px);
  }
  
  /* Swipe Gesture Indicators */
  .pwa-swipe-indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 40px;
    background: var(--color-heritage-gold);
    border-radius: var(--pwa-radius-pill);
    opacity: 0;
    transition: all var(--pwa-transition-fast);
  }
  
  .pwa-swipe-indicator--left {
    left: var(--pwa-space-xs);
  }
  
  .pwa-swipe-indicator--right {
    right: var(--pwa-space-xs);
  }
  
  .pwa-card:hover .pwa-swipe-indicator {
    opacity: 0.3;
  }
  
  /* Bottom Sheet Pattern */
  .pwa-bottom-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--pwa-z-modal);
    background: var(--color-heritage-cream);
    border-radius: var(--pwa-radius-xl) var(--pwa-radius-xl) 0 0;
    box-shadow: var(--pwa-shadow-xl);
    transform: translateY(100%);
    transition: transform var(--pwa-transition-slow) var(--pwa-spring);
    max-height: 80vh;
    overflow: hidden;
  }
  
  .pwa-bottom-sheet--open {
    transform: translateY(0);
  }
  
  .pwa-bottom-sheet__handle {
    width: 40px;
    height: 4px;
    background: rgba(89, 28, 40, 0.2);
    border-radius: var(--pwa-radius-pill);
    margin: var(--pwa-space-md) auto var(--pwa-space-lg);
  }
  
  .pwa-bottom-sheet__content {
    padding: 0 var(--pwa-space-lg) var(--pwa-space-xl);
    overflow-y: auto;
    max-height: calc(80vh - 60px);
  }
  
  /* Modern Button Styles */
  .pwa-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--pwa-space-sm);
    padding: var(--pwa-space-md) var(--pwa-space-lg);
    min-height: var(--pwa-touch-comfortable);
    border-radius: var(--pwa-radius-pill);
    font-weight: 600;
    font-size: 14px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all var(--pwa-transition-normal) var(--pwa-ease-out);
    position: relative;
    overflow: hidden;
  }
  
  .pwa-button--primary {
    background: var(--color-heritage-gold);
    color: var(--color-heritage-black);
  }
  
  .pwa-button--primary:hover {
    background: #F7D046;
    transform: translateY(-1px);
    box-shadow: var(--pwa-shadow-md);
  }
  
  .pwa-button--secondary {
    background: transparent;
    color: var(--color-heritage-forest);
    border-color: var(--color-heritage-forest);
  }
  
  .pwa-button--secondary:hover {
    background: var(--color-heritage-forest);
    color: var(--color-heritage-cream);
  }
  
  /* Haptic Feedback Simulation */
  .pwa-haptic {
    animation: haptic-tap var(--pwa-transition-fast) var(--pwa-spring);
  }
  
  @keyframes haptic-tap {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
  }
  
  /* Loading States */
  .pwa-loading {
    position: relative;
    overflow: hidden;
  }
  
  .pwa-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(247, 208, 70, 0.3),
      transparent
    );
    animation: loading-shimmer 1.5s infinite;
  }
  
  @keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  /* Safe Area Handling */
  .pwa-safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .pwa-safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .pwa-safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .pwa-safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* ===== PRESERVE DESKTOP STYLES ===== */
/* Desktop styles remain completely unchanged */
@media (min-width: 769px) {
  /* All existing desktop styles are preserved */
  /* PWA patterns are mobile-only */
}
