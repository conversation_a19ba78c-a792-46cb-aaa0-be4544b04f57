/**
 * NAROOP 2025 PWA Mobile Interactions
 * Modern mobile interaction patterns for PWA experience
 */

// ===== TOUCH GESTURE HANDLERS =====

export class PWAMobileInteractions {
  constructor() {
    this.isTouch = 'ontouchstart' in window;
    this.isMobile = window.innerWidth <= 768;
    this.pullRefreshThreshold = 80;
    this.swipeThreshold = 50;
    this.hapticSupported = 'vibrate' in navigator;
    
    this.init();
  }
  
  init() {
    if (!this.isMobile) return;
    
    this.setupPullToRefresh();
    this.setupSwipeGestures();
    this.setupHapticFeedback();
    this.setupBottomSheet();
    this.setupFAB();
  }
  
  // ===== PULL TO REFRESH =====
  setupPullToRefresh() {
    let startY = 0;
    let currentY = 0;
    let isPulling = false;
    
    const pullIndicator = document.querySelector('.pwa-pull-refresh');
    const scrollContainer = document.querySelector('.pwa-card-stack') || document.body;
    
    const handleTouchStart = (e) => {
      if (scrollContainer.scrollTop === 0) {
        startY = e.touches[0].clientY;
        isPulling = true;
      }
    };
    
    const handleTouchMove = (e) => {
      if (!isPulling) return;
      
      currentY = e.touches[0].clientY;
      const pullDistance = currentY - startY;
      
      if (pullDistance > 0 && pullDistance < this.pullRefreshThreshold * 2) {
        e.preventDefault();
        
        if (pullIndicator) {
          const progress = Math.min(pullDistance / this.pullRefreshThreshold, 1);
          pullIndicator.style.transform = `translateX(-50%) translateY(${pullDistance * 0.3}px) scale(${progress})`;
          pullIndicator.style.opacity = progress;
          
          if (pullDistance > this.pullRefreshThreshold) {
            pullIndicator.classList.add('pwa-pull-refresh--active');
            this.triggerHaptic('light');
          }
        }
      }
    };
    
    const handleTouchEnd = () => {
      if (!isPulling) return;
      
      const pullDistance = currentY - startY;
      
      if (pullDistance > this.pullRefreshThreshold) {
        this.triggerRefresh();
      }
      
      if (pullIndicator) {
        pullIndicator.style.transform = 'translateX(-50%) translateY(-60px) scale(0)';
        pullIndicator.style.opacity = '0';
        pullIndicator.classList.remove('pwa-pull-refresh--active');
      }
      
      isPulling = false;
      startY = 0;
      currentY = 0;
    };
    
    document.addEventListener('touchstart', handleTouchStart, { passive: false });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  }
  
  // ===== SWIPE GESTURES =====
  setupSwipeGestures() {
    let startX = 0;
    let startY = 0;
    let currentElement = null;
    
    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      currentElement = e.target.closest('.pwa-card');
    };
    
    const handleTouchEnd = (e) => {
      if (!currentElement) return;
      
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const deltaX = endX - startX;
      const deltaY = endY - startY;
      
      // Check if it's a horizontal swipe
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > this.swipeThreshold) {
        this.triggerHaptic('medium');
        
        if (deltaX > 0) {
          this.handleSwipeRight(currentElement);
        } else {
          this.handleSwipeLeft(currentElement);
        }
      }
      
      currentElement = null;
    };
    
    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchend', handleTouchEnd);
  }
  
  // ===== HAPTIC FEEDBACK =====
  setupHapticFeedback() {
    // Add haptic feedback to interactive elements
    const interactiveElements = document.querySelectorAll(
      '.pwa-button, .pwa-bottom-nav__item, .pwa-fab, .pwa-card'
    );
    
    interactiveElements.forEach(element => {
      element.addEventListener('touchstart', () => {
        this.triggerHaptic('light');
        element.classList.add('pwa-haptic');
        setTimeout(() => element.classList.remove('pwa-haptic'), 150);
      });
    });
  }
  
  triggerHaptic(intensity = 'light') {
    if (!this.hapticSupported) return;
    
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [50],
      success: [10, 50, 10],
      error: [100, 50, 100]
    };
    
    navigator.vibrate(patterns[intensity] || patterns.light);
  }
  
  // ===== BOTTOM SHEET =====
  setupBottomSheet() {
    const bottomSheets = document.querySelectorAll('.pwa-bottom-sheet');
    
    bottomSheets.forEach(sheet => {
      const handle = sheet.querySelector('.pwa-bottom-sheet__handle');
      let startY = 0;
      let currentY = 0;
      let isDragging = false;
      
      const handleTouchStart = (e) => {
        startY = e.touches[0].clientY;
        isDragging = true;
      };
      
      const handleTouchMove = (e) => {
        if (!isDragging) return;
        
        currentY = e.touches[0].clientY;
        const deltaY = currentY - startY;
        
        if (deltaY > 0) {
          sheet.style.transform = `translateY(${deltaY}px)`;
        }
      };
      
      const handleTouchEnd = () => {
        if (!isDragging) return;
        
        const deltaY = currentY - startY;
        
        if (deltaY > 100) {
          this.closeBottomSheet(sheet);
        } else {
          sheet.style.transform = 'translateY(0)';
        }
        
        isDragging = false;
      };
      
      if (handle) {
        handle.addEventListener('touchstart', handleTouchStart);
        handle.addEventListener('touchmove', handleTouchMove);
        handle.addEventListener('touchend', handleTouchEnd);
      }
    });
  }
  
  // ===== FLOATING ACTION BUTTON =====
  setupFAB() {
    const fab = document.querySelector('.pwa-fab');
    if (!fab) return;
    
    let lastScrollY = window.scrollY;
    
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down - hide FAB
        fab.style.transform = 'translateY(100px) scale(0.8)';
        fab.style.opacity = '0';
      } else {
        // Scrolling up - show FAB
        fab.style.transform = 'translateY(0) scale(1)';
        fab.style.opacity = '1';
      }
      
      lastScrollY = currentScrollY;
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
  }
  
  // ===== UTILITY METHODS =====
  
  triggerRefresh() {
    this.triggerHaptic('success');
    
    // Dispatch custom refresh event
    const refreshEvent = new CustomEvent('pwa-refresh', {
      detail: { timestamp: Date.now() }
    });
    document.dispatchEvent(refreshEvent);
    
    // Show loading state
    const pullIndicator = document.querySelector('.pwa-pull-refresh');
    if (pullIndicator) {
      pullIndicator.innerHTML = '⟳';
      pullIndicator.style.animation = 'spin 1s linear infinite';
    }
    
    // Simulate refresh completion
    setTimeout(() => {
      if (pullIndicator) {
        pullIndicator.innerHTML = '↓';
        pullIndicator.style.animation = '';
      }
    }, 1500);
  }
  
  handleSwipeLeft(element) {
    // Handle left swipe (e.g., delete, archive)
    element.style.transform = 'translateX(-100%)';
    element.style.opacity = '0';
    
    const swipeEvent = new CustomEvent('pwa-swipe-left', {
      detail: { element }
    });
    document.dispatchEvent(swipeEvent);
  }
  
  handleSwipeRight(element) {
    // Handle right swipe (e.g., like, save)
    element.style.transform = 'translateX(100%)';
    element.style.opacity = '0';
    
    const swipeEvent = new CustomEvent('pwa-swipe-right', {
      detail: { element }
    });
    document.dispatchEvent(swipeEvent);
  }
  
  openBottomSheet(sheetId) {
    const sheet = document.getElementById(sheetId);
    if (sheet) {
      sheet.classList.add('pwa-bottom-sheet--open');
      this.triggerHaptic('light');
    }
  }
  
  closeBottomSheet(sheet) {
    if (typeof sheet === 'string') {
      sheet = document.getElementById(sheet);
    }
    
    if (sheet) {
      sheet.classList.remove('pwa-bottom-sheet--open');
      this.triggerHaptic('light');
    }
  }
  
  showLoading(element) {
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }
    
    if (element) {
      element.classList.add('pwa-loading');
    }
  }
  
  hideLoading(element) {
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }
    
    if (element) {
      element.classList.remove('pwa-loading');
    }
  }
}

  // ===== INFINITE SCROLL =====
  setupInfiniteScroll(container, loadMoreCallback) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadMoreCallback();
          }
        });
      },
      { threshold: 0.1 }
    );

    const sentinel = document.createElement('div');
    sentinel.className = 'pwa-infinite-scroll-sentinel';
    sentinel.style.height = '1px';
    container.appendChild(sentinel);

    observer.observe(sentinel);
    return observer;
  }

  // ===== PERFORMANCE OPTIMIZATION =====
  optimizeScrollPerformance() {
    // Passive scroll listeners for better performance
    let ticking = false;

    const updateScrollPosition = () => {
      // Update scroll-dependent UI elements
      const scrollY = window.scrollY;
      const elements = document.querySelectorAll('[data-scroll-parallax]');

      elements.forEach(element => {
        const speed = element.dataset.scrollParallax || 0.5;
        element.style.transform = `translateY(${scrollY * speed}px)`;
      });

      ticking = false;
    };

    const requestScrollUpdate = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollPosition);
        ticking = true;
      }
    };

    window.addEventListener('scroll', requestScrollUpdate, { passive: true });
  }
}

// ===== AUTO-INITIALIZE =====
document.addEventListener('DOMContentLoaded', () => {
  if (window.innerWidth <= 768) {
    window.pwaInteractions = new PWAMobileInteractions();
  }
});

// ===== EXPORT FOR MANUAL INITIALIZATION =====
export default PWAMobileInteractions;
