import React, { useState, useEffect } from 'react';
import PWAMobileLayout from './PWAMobileLayout';
import './PWAMobileDemo.css';

/**
 * PWA Mobile Demo Component
 * Demonstrates 2025 PWA mobile patterns with sample NAROOP content
 */
const PWAMobileDemo = () => {
  const [currentSection, setCurrentSection] = useState('home');
  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const [stories, setStories] = useState([]);
  const [discussions, setDiscussions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Sample data
  useEffect(() => {
    const sampleStories = [
      {
        id: 1,
        title: "Breaking Barriers in Tech",
        excerpt: "My journey from the inner city to Silicon Valley, and how I'm using technology to uplift my community...",
        author: { name: "<PERSON>" },
        createdAt: "2 hours ago",
        likes: 24,
        comments: 8
      },
      {
        id: 2,
        title: "<PERSON>mother's Wisdom",
        excerpt: "The life lessons my grandmother taught me about resilience, faith, and the power of community...",
        author: { name: "<PERSON><PERSON>" },
        createdAt: "5 hours ago",
        likes: 42,
        comments: 15
      },
      {
        id: 3,
        title: "Building Black-Owned Businesses",
        excerpt: "How we created a network of Black entrepreneurs in our neighborhood and the impact it's having...",
        author: { name: "Jamal Thompson" },
        createdAt: "1 day ago",
        likes: 67,
        comments: 23
      }
    ];

    const sampleDiscussions = [
      {
        id: 1,
        title: "Supporting Black-Owned Restaurants",
        preview: "Let's share our favorite Black-owned restaurants and how we can support them better...",
        category: "Economic Empowerment",
        participants: 34,
        lastActivity: "30 minutes ago",
        isActive: true
      },
      {
        id: 2,
        title: "Mentorship for Young Professionals",
        preview: "Creating mentorship opportunities for young Black professionals entering the workforce...",
        category: "Career Development",
        participants: 18,
        lastActivity: "2 hours ago",
        isActive: true
      },
      {
        id: 3,
        title: "Community Garden Project",
        preview: "Planning a community garden to bring fresh food and unity to our neighborhood...",
        category: "Community Building",
        participants: 12,
        lastActivity: "1 day ago",
        isActive: false
      }
    ];

    setStories(sampleStories);
    setDiscussions(sampleDiscussions);
  }, []);

  const handleNavigation = (section) => {
    setCurrentSection(section);
    console.log('Navigated to:', section);
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Add new content to demonstrate refresh
    const newStory = {
      id: stories.length + 1,
      title: "Fresh Perspective",
      excerpt: "A new story has been added through pull-to-refresh...",
      author: { name: "New Author" },
      createdAt: "Just now",
      likes: 0,
      comments: 0
    };
    
    setStories(prev => [newStory, ...prev]);
    setIsLoading(false);
  };

  return (
    <div className="pwa-mobile-demo">
      <PWAMobileLayout
        currentSection={currentSection}
        onNavigate={handleNavigation}
        isAuthenticated={isAuthenticated}
        stories={stories}
        discussions={discussions}
        onRefresh={handleRefresh}
        userRole="user"
        ADMIN_ROLES={['admin', 'moderator']}
      >
        {/* Demo Content */}
        <div className="pwa-demo-content">
          
          {/* Welcome Section */}
          <div className="pwa-demo-section">
            <h2 className="pwa-demo-title">Welcome to NAROOP PWA</h2>
            <p className="pwa-demo-description">
              Experience modern 2025 mobile design patterns while preserving 
              NAROOP's cultural authenticity and community focus.
            </p>
          </div>

          {/* Feature Highlights */}
          <div className="pwa-demo-section">
            <h3 className="pwa-demo-subtitle">PWA Features</h3>
            <div className="pwa-demo-features">
              <div className="pwa-demo-feature">
                <span className="pwa-demo-feature-icon">👆</span>
                <div>
                  <strong>Swipe Gestures</strong>
                  <p>Swipe cards left/right for quick actions</p>
                </div>
              </div>
              
              <div className="pwa-demo-feature">
                <span className="pwa-demo-feature-icon">📱</span>
                <div>
                  <strong>Haptic Feedback</strong>
                  <p>Feel the interactions with vibration</p>
                </div>
              </div>
              
              <div className="pwa-demo-feature">
                <span className="pwa-demo-feature-icon">🔄</span>
                <div>
                  <strong>Pull to Refresh</strong>
                  <p>Pull down to refresh content</p>
                </div>
              </div>
              
              <div className="pwa-demo-feature">
                <span className="pwa-demo-feature-icon">⚡</span>
                <div>
                  <strong>Bottom Navigation</strong>
                  <p>Modern tab-based navigation</p>
                </div>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="pwa-demo-section">
            <h3 className="pwa-demo-subtitle">Try These Interactions</h3>
            <div className="pwa-demo-instructions">
              <div className="pwa-demo-instruction">
                <span>1.</span>
                <p>Pull down from the top to refresh content</p>
              </div>
              <div className="pwa-demo-instruction">
                <span>2.</span>
                <p>Swipe story cards right to like, left to share</p>
              </div>
              <div className="pwa-demo-instruction">
                <span>3.</span>
                <p>Tap the floating action button for quick actions</p>
              </div>
              <div className="pwa-demo-instruction">
                <span>4.</span>
                <p>Use bottom navigation to switch sections</p>
              </div>
            </div>
          </div>

          {/* Community Stats */}
          <div className="pwa-demo-section">
            <h3 className="pwa-demo-subtitle">Community Impact</h3>
            <div className="pwa-demo-stats">
              <div className="pwa-demo-stat">
                <span className="pwa-demo-stat-number">1,247</span>
                <span className="pwa-demo-stat-label">Stories Shared</span>
              </div>
              <div className="pwa-demo-stat">
                <span className="pwa-demo-stat-number">892</span>
                <span className="pwa-demo-stat-label">Active Members</span>
              </div>
              <div className="pwa-demo-stat">
                <span className="pwa-demo-stat-number">156</span>
                <span className="pwa-demo-stat-label">Discussions</span>
              </div>
            </div>
          </div>

        </div>
      </PWAMobileLayout>
    </div>
  );
};

export default PWAMobileDemo;
