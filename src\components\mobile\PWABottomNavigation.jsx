import React, { useState, useEffect } from 'react';
import './PWABottomNavigation.css';

/**
 * Modern 2025 PWA Bottom Navigation Component
 * Implements latest mobile navigation patterns while preserving NAROOP design
 */
const PWABottomNavigation = ({
  currentSection = 'home',
  onNavigate,
  userRole,
  ADMIN_ROLES,
  isAuthenticated = false
}) => {
  const [activeSection, setActiveSection] = useState(currentSection);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Don't render on desktop
  if (!isMobile) return null;

  const navigationItems = [
    {
      id: 'home',
      label: 'Home',
      icon: '🏠',
      description: 'Community Feed'
    },
    {
      id: 'stories',
      label: 'Stories',
      icon: '📖',
      description: 'Share & Browse'
    },
    {
      id: 'connect',
      label: 'Connect',
      icon: '🤝',
      description: 'Community Hub'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: '👤',
      description: 'Your Account'
    }
  ];

  // Add Kids Zone if authenticated
  if (isAuthenticated) {
    navigationItems.splice(3, 0, {
      id: 'kids',
      label: 'Kids',
      icon: '🌟',
      description: 'Kids Zone',
      isSpecial: true
    });
  }

  const handleNavigation = (sectionId) => {
    setActiveSection(sectionId);
    
    // Trigger haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate([10]);
    }
    
    // Add visual feedback
    const button = document.querySelector(`[data-nav-id="${sectionId}"]`);
    if (button) {
      button.classList.add('pwa-haptic');
      setTimeout(() => button.classList.remove('pwa-haptic'), 150);
    }
    
    if (onNavigate) {
      onNavigate(sectionId);
    }
  };

  return (
    <>
      {/* PWA Bottom Navigation */}
      <nav className="pwa-bottom-nav" role="navigation" aria-label="Main navigation">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            data-nav-id={item.id}
            className={`pwa-bottom-nav__item ${
              activeSection === item.id ? 'pwa-bottom-nav__item--active' : ''
            } ${item.isSpecial ? 'pwa-bottom-nav__item--special' : ''}`}
            onClick={() => handleNavigation(item.id)}
            aria-label={`Navigate to ${item.label}: ${item.description}`}
            aria-current={activeSection === item.id ? 'page' : undefined}
          >
            <div className="pwa-bottom-nav__icon">
              {item.icon}
            </div>
            <div className="pwa-bottom-nav__label">
              {item.label}
            </div>
            
            {/* Active indicator */}
            {activeSection === item.id && (
              <div className="pwa-bottom-nav__indicator" />
            )}
            
            {/* Special indicator for Kids Zone */}
            {item.isSpecial && (
              <div className="pwa-bottom-nav__special-badge">
                ✨
              </div>
            )}
          </button>
        ))}
      </nav>

      {/* Floating Action Button for Quick Story Share */}
      {isAuthenticated && (
        <button
          className="pwa-fab"
          onClick={() => handleNavigation('share-story')}
          aria-label="Share your story"
          title="Share your story"
        >
          <span className="pwa-fab__icon">✍️</span>
        </button>
      )}

      {/* Bottom Sheet for Quick Actions */}
      <div id="pwa-quick-actions" className="pwa-bottom-sheet">
        <div className="pwa-bottom-sheet__handle" />
        <div className="pwa-bottom-sheet__content">
          <h3 className="pwa-bottom-sheet__title">Quick Actions</h3>
          
          <div className="pwa-quick-actions">
            <button className="pwa-button pwa-button--primary">
              <span>📝</span>
              Share Story
            </button>
            <button className="pwa-button pwa-button--secondary">
              <span>💬</span>
              Start Discussion
            </button>
            <button className="pwa-button pwa-button--secondary">
              <span>🤝</span>
              Request Support
            </button>
            <button className="pwa-button pwa-button--secondary">
              <span>💼</span>
              Business Directory
            </button>
          </div>
        </div>
      </div>

      {/* Pull to Refresh Indicator */}
      <div className="pwa-pull-refresh">
        <span>↓</span>
      </div>
    </>
  );
};

export default PWABottomNavigation;
