/* NAROOP PWA Bottom Navigation - 2025 Mobile Patterns */
/* Preserving NAROOP color palette: #FDFBF5, #591C28, #6E8C65, #F7D046 */

/* ===== PWA BOTTOM NAVIGATION ===== */
.pwa-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(253, 251, 245, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 1px solid rgba(89, 28, 40, 0.08);
  padding: 8px 16px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4px 20px rgba(89, 28, 40, 0.12);
  
  /* Safe area handling for modern devices */
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
}

.pwa-bottom-nav__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  min-width: 48px;
  min-height: 48px;
  border-radius: 12px;
  border: none;
  background: transparent;
  color: var(--color-heritage-forest);
  text-decoration: none;
  cursor: pointer;
  transition: all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.pwa-bottom-nav__item:hover {
  background: rgba(110, 140, 101, 0.08);
  transform: translateY(-1px);
}

.pwa-bottom-nav__item--active {
  background: rgba(247, 208, 70, 0.15);
  color: var(--color-heritage-black);
  transform: translateY(-2px);
}

.pwa-bottom-nav__item--active::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: var(--color-heritage-gold);
  border-radius: 50px;
  animation: indicator-appear 300ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes indicator-appear {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 24px;
    opacity: 1;
  }
}

.pwa-bottom-nav__icon {
  font-size: 20px;
  line-height: 1;
  transition: transform 150ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

.pwa-bottom-nav__item--active .pwa-bottom-nav__icon {
  transform: scale(1.1);
}

.pwa-bottom-nav__label {
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  line-height: 1;
  letter-spacing: 0.02em;
}

.pwa-bottom-nav__item--special {
  position: relative;
}

.pwa-bottom-nav__special-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 8px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* ===== FLOATING ACTION BUTTON ===== */
.pwa-fab {
  position: fixed;
  bottom: calc(64px + 24px + env(safe-area-inset-bottom));
  right: 16px;
  z-index: 999;
  width: 56px;
  height: 56px;
  border-radius: 50px;
  background: linear-gradient(135deg, var(--color-heritage-gold), #F7D046);
  border: none;
  box-shadow: 0 8px 24px rgba(89, 28, 40, 0.16);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-heritage-black);
  cursor: pointer;
  transition: all 250ms cubic-bezier(0.34, 1.56, 0.64, 1);
  opacity: 1;
  transform: translateY(0) scale(1);
}

.pwa-fab:hover,
.pwa-fab:focus {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 12px 32px rgba(89, 28, 40, 0.20);
}

.pwa-fab:active {
  transform: scale(0.95);
}

.pwa-fab__icon {
  font-size: 24px;
  line-height: 1;
}

/* ===== BOTTOM SHEET ===== */
.pwa-bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2000;
  background: var(--color-heritage-cream);
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -8px 32px rgba(89, 28, 40, 0.16);
  transform: translateY(100%);
  transition: transform 350ms cubic-bezier(0.34, 1.56, 0.64, 1);
  max-height: 80vh;
  overflow: hidden;
}

.pwa-bottom-sheet--open {
  transform: translateY(0);
}

.pwa-bottom-sheet__handle {
  width: 40px;
  height: 4px;
  background: rgba(89, 28, 40, 0.2);
  border-radius: 50px;
  margin: 16px auto 24px;
  cursor: grab;
  transition: background-color 200ms ease;
}

.pwa-bottom-sheet__handle:hover {
  background: rgba(89, 28, 40, 0.3);
}

.pwa-bottom-sheet__content {
  padding: 0 24px 32px;
  overflow-y: auto;
  max-height: calc(80vh - 60px);
}

.pwa-bottom-sheet__title {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-heritage-black);
  margin: 0 0 24px;
  text-align: center;
}

/* ===== QUICK ACTIONS ===== */
.pwa-quick-actions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.pwa-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  min-height: 48px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 14px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.pwa-button--primary {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
}

.pwa-button--primary:hover {
  background: #F7D046;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.12);
}

.pwa-button--secondary {
  background: transparent;
  color: var(--color-heritage-forest);
  border-color: var(--color-heritage-forest);
}

.pwa-button--secondary:hover {
  background: var(--color-heritage-forest);
  color: var(--color-heritage-cream);
}

/* ===== PULL TO REFRESH ===== */
.pwa-pull-refresh {
  position: fixed;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50px;
  background: rgba(247, 208, 70, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-heritage-black);
  font-size: 18px;
  font-weight: bold;
  transition: all 250ms cubic-bezier(0.34, 1.56, 0.64, 1);
  opacity: 0;
  pointer-events: none;
  z-index: 1001;
}

.pwa-pull-refresh--active {
  opacity: 1;
  transform: translateX(-50%) translateY(20px);
}

/* ===== HAPTIC FEEDBACK ANIMATION ===== */
.pwa-haptic {
  animation: haptic-tap 150ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes haptic-tap {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* ===== LOADING STATES ===== */
.pwa-loading {
  position: relative;
  overflow: hidden;
}

.pwa-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(247, 208, 70, 0.3),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .pwa-bottom-nav__item,
  .pwa-fab,
  .pwa-bottom-sheet,
  .pwa-button {
    transition-duration: 0.01ms;
  }
  
  .pwa-haptic {
    animation: none;
  }
  
  @keyframes sparkle,
  @keyframes indicator-appear,
  @keyframes loading-shimmer {
    animation-duration: 0.01ms;
  }
}

@media (prefers-contrast: high) {
  .pwa-bottom-nav {
    border-top-width: 2px;
  }
  
  .pwa-button {
    border-width: 3px;
  }
  
  .pwa-bottom-nav__item--active::before {
    height: 4px;
  }
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 320px) {
  .pwa-bottom-nav {
    padding: 6px 8px;
  }
  
  .pwa-bottom-nav__item {
    min-width: 40px;
    min-height: 40px;
    padding: 6px 2px;
  }
  
  .pwa-bottom-nav__icon {
    font-size: 18px;
  }
  
  .pwa-bottom-nav__label {
    font-size: 9px;
  }
  
  .pwa-fab {
    width: 48px;
    height: 48px;
    right: 12px;
  }
  
  .pwa-fab__icon {
    font-size: 20px;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (max-height: 500px) and (orientation: landscape) {
  .pwa-bottom-nav {
    padding: 4px 16px;
  }
  
  .pwa-bottom-nav__item {
    flex-direction: row;
    gap: 6px;
    padding: 4px 8px;
    min-height: 36px;
  }
  
  .pwa-bottom-nav__icon {
    font-size: 16px;
  }
  
  .pwa-bottom-nav__label {
    font-size: 9px;
  }
  
  .pwa-fab {
    width: 44px;
    height: 44px;
    bottom: calc(44px + 16px);
  }
}
