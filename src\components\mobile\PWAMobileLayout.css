/* NAROOP PWA Mobile Layout - 2025 Design System */
/* Preserving NAROOP color palette: #FDFBF5, #591C28, #6E8C65, #F7D046 */

/* ===== PWA MOBILE LAYOUT ===== */
.pwa-mobile-layout {
  /* Only apply on mobile devices */
  display: block;
}

@media (min-width: 769px) {
  .pwa-mobile-layout {
    display: contents; /* Transparent wrapper on desktop */
  }
}

/* ===== PWA APP SHELL ===== */
.pwa-app-shell {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--color-heritage-cream);
  position: relative;
  overflow-x: hidden;
  
  /* Safe area handling for modern devices */
  padding-top: env(safe-area-inset-top);
}

/* ===== MAIN CONTENT AREA ===== */
.pwa-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: calc(80px + env(safe-area-inset-bottom)); /* Space for bottom nav */
  overflow-y: auto;
  overflow-x: hidden;
  
  /* Smooth scrolling */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* ===== REFRESH INDICATOR ===== */
.pwa-refresh-indicator {
  position: fixed;
  top: calc(20px + env(safe-area-inset-top));
  left: 50%;
  transform: translateX(-50%);
  z-index: 2000;
  background: rgba(247, 208, 70, 0.95);
  color: var(--color-heritage-black);
  padding: 12px 20px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(89, 28, 40, 0.16);
  backdrop-filter: blur(10px);
  animation: refresh-slide-in 300ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes refresh-slide-in {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.pwa-refresh-spinner {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== CARD STACK CONTAINER ===== */
.pwa-card-stack {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

/* ===== INFINITE SCROLL SENTINEL ===== */
.pwa-infinite-scroll-sentinel {
  height: 1px;
  width: 100%;
  margin: 20px 0;
  opacity: 0;
  pointer-events: none;
}

/* ===== BOTTOM SHEET OVERLAY ===== */
.pwa-bottom-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1999;
  background: rgba(26, 26, 26, 0.4);
  backdrop-filter: blur(4px);
  animation: overlay-fade-in 200ms ease;
}

@keyframes overlay-fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* ===== ENHANCED QUICK ACTIONS ===== */
.pwa-quick-actions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 8px;
}

.pwa-quick-actions .pwa-button {
  justify-content: flex-start;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.pwa-quick-actions .pwa-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 300ms ease;
}

.pwa-quick-actions .pwa-button:hover::before {
  left: 100%;
}

/* ===== FLOATING ACTION BUTTON ENHANCEMENTS ===== */
.pwa-mobile-layout .pwa-fab {
  /* Enhanced positioning for mobile layout */
  bottom: calc(80px + 24px + env(safe-area-inset-bottom));
  right: 16px;
  
  /* Enhanced visual effects */
  background: linear-gradient(135deg, var(--color-heritage-gold), #F7D046);
  box-shadow: 
    0 8px 24px rgba(89, 28, 40, 0.16),
    0 0 0 0 rgba(247, 208, 70, 0.4);
  
  /* Pulse animation for attention */
  animation: fab-pulse 3s ease-in-out infinite;
}

@keyframes fab-pulse {
  0%, 100% {
    box-shadow: 
      0 8px 24px rgba(89, 28, 40, 0.16),
      0 0 0 0 rgba(247, 208, 70, 0.4);
  }
  50% {
    box-shadow: 
      0 8px 24px rgba(89, 28, 40, 0.16),
      0 0 0 8px rgba(247, 208, 70, 0);
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.pwa-mobile-layout * {
  /* Hardware acceleration for smooth animations */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.pwa-card-stack {
  /* Optimize scrolling performance */
  contain: layout style paint;
  will-change: scroll-position;
}

.pwa-mobile-card {
  /* Optimize card rendering */
  contain: layout style paint;
  will-change: transform, opacity;
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.pwa-mobile-layout {
  /* Ensure proper focus management */
  isolation: isolate;
}

.pwa-mobile-layout *:focus {
  outline: 3px solid var(--color-heritage-gold);
  outline-offset: 2px;
  z-index: 1;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 320px) {
  .pwa-card-stack {
    padding: 12px;
    gap: 12px;
  }
  
  .pwa-refresh-indicator {
    padding: 8px 16px;
    font-size: 12px;
  }
  
  .pwa-mobile-layout .pwa-fab {
    width: 48px;
    height: 48px;
    right: 12px;
  }
}

@media (min-width: 375px) and (max-width: 768px) {
  .pwa-card-stack {
    max-width: 600px;
    padding: 20px;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (max-height: 500px) and (orientation: landscape) {
  .pwa-main-content {
    padding-bottom: 60px; /* Reduced space for landscape */
  }
  
  .pwa-card-stack {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .pwa-mobile-layout .pwa-fab {
    bottom: calc(60px + 16px);
    width: 44px;
    height: 44px;
  }
  
  .pwa-refresh-indicator {
    top: 12px;
    padding: 8px 16px;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .pwa-app-shell {
    border: 2px solid var(--color-heritage-black);
  }
  
  .pwa-refresh-indicator {
    border: 2px solid var(--color-heritage-black);
    box-shadow: 0 4px 16px rgba(89, 28, 40, 0.3);
  }
  
  .pwa-bottom-sheet-overlay {
    background: rgba(0, 0, 0, 0.7);
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .pwa-app-shell,
  .pwa-refresh-indicator,
  .pwa-mobile-layout .pwa-fab,
  .pwa-quick-actions .pwa-button {
    animation: none;
    transition-duration: 0.01ms;
  }
  
  .pwa-main-content {
    scroll-behavior: auto;
  }
  
  @keyframes refresh-slide-in,
  @keyframes spin,
  @keyframes overlay-fade-in,
  @keyframes fab-pulse {
    animation-duration: 0.01ms;
  }
}

/* ===== DARK MODE SUPPORT (Future Enhancement) ===== */
@media (prefers-color-scheme: dark) {
  .pwa-app-shell {
    /* Maintain NAROOP light theme for cultural authenticity */
    /* Dark mode can be added as user preference later */
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .pwa-mobile-layout .pwa-fab,
  .pwa-bottom-sheet,
  .pwa-refresh-indicator {
    display: none;
  }
  
  .pwa-card-stack {
    gap: 8px;
  }
  
  .pwa-mobile-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}
