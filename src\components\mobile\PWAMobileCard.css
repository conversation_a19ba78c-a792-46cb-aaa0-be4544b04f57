/* NAROOP PWA Mobile Card - 2025 Design Patterns */
/* Preserving NAROOP color palette: #FDFBF5, #591C28, #6E8C65, #F7D046 */

/* ===== BASE CARD STYLES ===== */
.pwa-mobile-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin: 12px 0;
  box-shadow: 0 2px 12px rgba(89, 28, 40, 0.08);
  border: 1px solid rgba(89, 28, 40, 0.06);
  transition: all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  touch-action: pan-y;
}

.pwa-mobile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(89, 28, 40, 0.12);
}

.pwa-mobile-card--pressed {
  transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(89, 28, 40, 0.06);
}

/* ===== CARD VARIANTS ===== */
.pwa-mobile-card--story::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-heritage-gold), #F7D046);
}

.pwa-mobile-card--discussion::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-heritage-forest), #6E8C65);
}

.pwa-mobile-card--support::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-heritage-black), #591C28);
}

/* ===== PRIORITY INDICATORS ===== */
.pwa-mobile-card--high {
  border-left: 4px solid var(--color-heritage-gold);
}

.pwa-mobile-card__priority-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-heritage-gold);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  animation: priority-pulse 2s ease-in-out infinite;
}

@keyframes priority-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

/* ===== SWIPE INDICATORS ===== */
.pwa-mobile-card__swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(247, 208, 70, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  opacity: 0;
  transition: all 200ms ease;
  pointer-events: none;
}

.pwa-mobile-card__swipe-indicator--left {
  left: 12px;
}

.pwa-mobile-card__swipe-indicator--right {
  right: 12px;
}

.pwa-mobile-card--swipe-left .pwa-mobile-card__swipe-indicator--left,
.pwa-mobile-card--swipe-right .pwa-mobile-card__swipe-indicator--right {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* ===== CARD CONTENT ===== */
.pwa-mobile-card__content {
  position: relative;
  z-index: 1;
}

/* ===== INTERACTION HINTS ===== */
.pwa-mobile-card__hints {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16px;
  opacity: 0;
  transition: opacity 200ms ease;
  pointer-events: none;
}

.pwa-mobile-card:hover .pwa-mobile-card__hints {
  opacity: 0.6;
}

.pwa-mobile-card__hint {
  font-size: 10px;
  color: var(--color-heritage-forest);
  background: rgba(253, 251, 245, 0.9);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(89, 28, 40, 0.1);
}

/* ===== STORY CARD SPECIFIC STYLES ===== */
.pwa-story-card__header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.pwa-story-card__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-heritage-gold);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--color-heritage-black);
  font-size: 16px;
}

.pwa-story-card__meta {
  flex: 1;
}

.pwa-story-card__author {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0 0 4px;
}

.pwa-story-card__date {
  font-size: 12px;
  color: var(--color-heritage-forest);
  margin: 0;
}

.pwa-story-card__content {
  margin-bottom: 16px;
}

.pwa-story-card__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0 0 8px;
  line-height: 1.4;
}

.pwa-story-card__excerpt {
  font-size: 14px;
  color: var(--color-heritage-forest);
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pwa-story-card__actions {
  display: flex;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid rgba(89, 28, 40, 0.08);
}

.pwa-story-card__action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  background: transparent;
  border-radius: 20px;
  font-size: 12px;
  color: var(--color-heritage-forest);
  cursor: pointer;
  transition: all 200ms ease;
}

.pwa-story-card__action:hover {
  background: rgba(110, 140, 101, 0.1);
  color: var(--color-heritage-black);
}

/* ===== DISCUSSION CARD SPECIFIC STYLES ===== */
.pwa-discussion-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.pwa-discussion-card__category {
  font-size: 11px;
  font-weight: 600;
  color: var(--color-heritage-forest);
  background: rgba(110, 140, 101, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pwa-discussion-card__participants {
  font-size: 11px;
  color: var(--color-heritage-forest);
}

.pwa-discussion-card__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0 0 8px;
  line-height: 1.4;
}

.pwa-discussion-card__preview {
  font-size: 14px;
  color: var(--color-heritage-forest);
  line-height: 1.5;
  margin: 0 0 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pwa-discussion-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid rgba(89, 28, 40, 0.08);
}

.pwa-discussion-card__time {
  font-size: 12px;
  color: var(--color-heritage-forest);
}

.pwa-discussion-card__status {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(247, 208, 70, 0.1);
  color: var(--color-heritage-black);
}

/* ===== LOADING STATES ===== */
.pwa-mobile-card--loading {
  position: relative;
  overflow: hidden;
}

.pwa-mobile-card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(247, 208, 70, 0.2),
    transparent
  );
  animation: card-loading-shimmer 1.5s infinite;
}

@keyframes card-loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== ACCESSIBILITY ===== */
.pwa-mobile-card:focus {
  outline: 3px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .pwa-mobile-card,
  .pwa-mobile-card__swipe-indicator,
  .pwa-mobile-card__hints,
  .pwa-story-card__action {
    transition-duration: 0.01ms;
  }
  
  .pwa-mobile-card__priority-indicator {
    animation: none;
  }
  
  @keyframes priority-pulse,
  @keyframes card-loading-shimmer {
    animation-duration: 0.01ms;
  }
}

@media (prefers-contrast: high) {
  .pwa-mobile-card {
    border-width: 2px;
    box-shadow: 0 4px 16px rgba(89, 28, 40, 0.2);
  }
  
  .pwa-mobile-card--story::before,
  .pwa-mobile-card--discussion::before,
  .pwa-mobile-card--support::before {
    height: 4px;
  }
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 320px) {
  .pwa-mobile-card {
    padding: 16px;
    margin: 8px 0;
  }
  
  .pwa-story-card__header {
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .pwa-story-card__avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .pwa-story-card__actions {
    gap: 12px;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (max-height: 500px) and (orientation: landscape) {
  .pwa-mobile-card {
    padding: 12px 16px;
    margin: 6px 0;
  }
  
  .pwa-story-card__excerpt,
  .pwa-discussion-card__preview {
    -webkit-line-clamp: 2;
  }
}
